import * as THREE from "three";
import { MaterialBlendTex } from "./MaterialBlendTex";
import { MaterialGlobalProcess } from "./MaterialGlobalProcess";

/**
* @description 立方体材质管理器
* <AUTHOR>
* @date 2025-07-15
* @lastEditTime 2025-07-15 16:53:35
* @lastEditors xuld
*/
export class CubeMaterialMgr {

    // 立方体六面在大图中的位置和尺寸
    // [     ][top  ][     ]
    // [right][front][left ]
    // [     ][down ][     ]
    // [     ][back ][     ]
    private static readonly faceInfos = [
        { name: 'left', x: 0, y: 1024 }, // 右
        { name: 'right', x: 2048, y: 1024 }, // 左
        { name: 'top', x: 1024, y: 0 }, // 上
        { name: 'down', x: 1024, y: 2048 }, // 下
        { name: 'front', x: 1024, y: 1024 }, // 前
        { name: 'back', x: 1024, y: 3072 }, // 后
    ];

    /**
    * @description 获取图片URL
    * @param schemeId 方案ID
    * @param queueId 队列ID
    * @return string 图片URL
    */
    private static getFullUrl(schemeId: string, queueId: string): string {
        const host = 'https://img3.admin.3vjia.com';
        const url = `${host}/UpFile_Render/C00000022/DesignSchemeRender360File/${schemeId}/${queueId}.jpg`;
        return url;
    }

    /**
    * @description 获取立方体纹理图片URL
    * @return string 图片URL
    */
    private static getImageUrl(): string {
        const params = new URLSearchParams(window.location.search);
        let schemeId = params.get('schemeId');
        let queueId = params.get('queueId');
        if (!schemeId || !queueId) {
            // 如果方案ID或队列ID为空，则使用默认值
            schemeId = "138176672";
            queueId = "QO202505160000000000000874504550";
            console.warn('schemeId or queueId is not set, using default values');
        } else {
            console.log('schemeId and queueId', schemeId, queueId);
        }

        let imgUrl = CubeMaterialMgr.getFullUrl(schemeId, queueId);
        if (!imgUrl) {
            // 如果图片URL为空，则使用默认值
            console.warn('imgUrl is not set, using default values');
            imgUrl = "test_pano_img";
        }
        return imgUrl;
    }

    /**
    * @description 获取立方体纹理
    * @return Promise<THREE.Texture[]> 立方体纹理
    */
    public static async getCubeTextures(): Promise<THREE.Texture[]> {
        const timeout = 30000;
        const imgUrl = CubeMaterialMgr.getImageUrl();

        return new Promise<THREE.Texture[]>((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                reject(new Error("获取超时"));
            }, timeout);

            const loader = new THREE.ImageLoader();
            loader.load(imgUrl, (image) => {
                clearTimeout(timeoutId);
                const textures = CubeMaterialMgr.faceInfos.map(face => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 1024;
                    canvas.height = 1024;
                    const ctx = canvas.getContext('2d')!;
                    ctx.save();

                    // 后面对Y轴翻转
                    if (face.name === 'back') {
                        ctx.translate(0, 1024);
                        ctx.scale(1, -1);
                    } else {
                        // 其他面X轴翻转
                        ctx.translate(1024, 0);
                        ctx.scale(-1, 1);
                    }

                    ctx.drawImage(
                        image,
                        face.x, face.y, 1024, 1024,
                        0, 0, 1024, 1024
                    );
                    ctx.restore();
                    const texture = new THREE.CanvasTexture(canvas);
                    return texture;
                });
                resolve(textures);
            }, undefined, (error) => {
                clearTimeout(timeoutId);
                reject(new Error("图片加载失败"));
            });
        });
    }

    /**
    * @description 获取立方体材质
    * @param materialMode 材质模式
    * 0: MeshBasicMaterial
    * 1: 混合纹理材质
    * 2: 全局色器材质
    * @return Promise<THREE.Material[]> 立方体材质
    */
    public static async getCubeMaterials(materialMode: number): Promise<THREE.Material[]> {
        const textures = await CubeMaterialMgr.getCubeTextures();
        return textures.map(texture => {
            let material;
            if (materialMode === 1) {
                // 对于自定义着色器，我们不需要 sRGB 转换，因为我们在着色器中手动处理
                texture.colorSpace = THREE.NoColorSpace;
                material = MaterialBlendTex.createMaterial(texture);
            } else if (materialMode === 2) {
                // 对于自定义着色器，我们不需要 sRGB 转换，因为我们在着色器中手动处理
                texture.colorSpace = THREE.NoColorSpace;
                material = MaterialGlobalProcess.createMaterial(texture);
            } else {
                texture.colorSpace = THREE.SRGBColorSpace;
                material = new THREE.MeshBasicMaterial({ map: texture, side: THREE.BackSide });
            }

            material.side = THREE.BackSide;
            return material;
        });
    }
}